import { Prop } from "@automerge/automerge-repo";
import { ChartType } from "@oneteam/onetheme";

import { getByPath } from "@helpers/configurationFormHelper.ts";

import { Question } from "@src/types/Question.ts";
import {
  CartesianChartConfig,
  ChartConfig,
  ChartQuestionProperties,
  ListQuestionProperties,
  PieChartConfig
} from "@src/types/QuestionProperties.ts";
import { WorkspaceDocument } from "@src/types/documentTypes.ts";

// export const chartOnChange = ({
//   field,
//   path,
//   docChange
// }: {
//   field: keyof ChartConfig;
//   path: Prop[];
//   docChange: (cb: (d: WorkspaceDocument) => void) => void;
// }) => {
//   return (value: string) => {
//     docChange((d: WorkspaceDocument): void => {
//       const q = getByPath<Question<ChartQuestionProperties>>(d, path);
//       if (!q?.properties) {
//         console.error("Question not found", path);
//         return;
//       }

//       if (value === undefined || value === "") {
//         delete q.properties.chartConfig[field];
//         return;
//       }

//       // q.properties.chartConfig[field] = value;

//       // switch (field) {
//       //   case "minLength":
//       //   case "maxLength": {
//       //     if (!value) {
//       //       delete q.properties[field];
//       //     } else {
//       //       q.properties[field] = value as number;
//       //     }
//       //     break;
//       //   }
//       // }

//       const chartConfig = q.properties.chartConfig;

//       // Type guard for Cartesian charts (LINE, BAR)
//       if (
//         chartConfig.type === ChartType.LINE ||
//         chartConfig.type === ChartType.BAR
//       ) {
//         // chartConfig is CartesianChartConfig
//         // Only update fields that exist on CartesianChartConfig
//         if (
//           field === "xAxis" ||
//           field === "series" ||
//           field === "subType" ||
//           field === "swapColumns"
//         ) {
//           (chartConfig as CartesianChartConfig<ChartType.LINE | ChartType.BAR>)[
//             field
//           ] = value as any;
//         }
//       } else if (chartConfig.type === ChartType.PIE) {
//         // chartConfig is PieChartConfig
//         // Only update fields that exist on PieChartConfig
//         if (
//           field === "groupBy" ||
//           field === "series" ||
//           field === "rowIndex" ||
//           field === "subType" ||
//           field === "swapColumns"
//         ) {
//           (chartConfig as PieChartConfig)[field] = value as any;
//         }
//       }
//     });
//   };
// };
export const chartOnChange = ({
  type,
  field,
  path,
  docChange
}: {
  type: ChartType;
  field: string;
  path: Prop[];
  docChange: (cb: (d: WorkspaceDocument) => void) => void;
}) => {
  return (value: any) => {
    docChange((d: WorkspaceDocument): void => {
      const q = getByPath<Question<ChartQuestionProperties>>(d, path);
      if (!q?.properties) {
        console.error("Question not found", path);
        return;
      }

      if (value === undefined || value === "") {
        delete (q.properties.chartConfig as any)[field];
        return;
      }

      // Use the type to determine the config type
      if (type === ChartType.LINE || type === ChartType.BAR) {
        // CartesianChartConfig
        (q.properties.chartConfig as CartesianChartConfig<ChartType.LINE | ChartType.BAR>)[field as ] = value;
      } else if (type === ChartType.PIE) {
        // PieChartConfig
        (q.properties.chartConfig as PieChartConfig)[field] = value;
      }
    });
  };
};