import React, { useCallback, useMemo } from "react";
import { useOutletContext } from "react-router-dom";

import { Doc, Prop } from "@automerge/automerge-repo";
import { ChartType, MultiSelect, Select, Stack } from "@oneteam/onetheme";

import { DocChange } from "@pages/workspace/WorkspaceLayout.tsx";

import { Dictionary } from "@src/hooks/useDictionary.tsx";
import { ConfigurationFormMode } from "@src/types/FormConfiguration";
import { ChartSubTypes, Question } from "@src/types/Question.ts";
import {
  ChartQuestionProperties,
  ListQuestionProperties,
  TableQuestionProperties
} from "@src/types/QuestionProperties";
import { WorkspaceDocument } from "@src/types/documentTypes.ts";

import { MultiLevelProperties } from "../MultiLevel/MultiLevelProperties";
import { MinMaxLengthFields } from "../Text/TextProperties";

export const ChartTypeProperties = ({
  question,
  parentQuestion,
  path,
  d,
  isExpanded = true,
  onChangeExpanded,
  disabled
}: {
  question: Question<ChartQuestionProperties>;
  parentQuestion?: Question<TableQuestionProperties>;
  path: Prop[];
  d: Dictionary;
  isExpanded?: boolean;
  onChangeExpanded?: (expanded: boolean) => void;
  disabled?: boolean;
}) => {
  const { docChange } = useOutletContext<{
    document: Doc<WorkspaceDocument>;
    docChange: DocChange;
  }>();

  const questionAccessor = useCallback(
    (accessor: string) => {
      return `${path.join(".")}.${accessor}`;
    },
    [path]
  );

  const chartType = question?.properties?.chartConfig?.type;
  const subTypeOptions = useMemo(() => {
    const subTypes =
      chartType && ChartSubTypes[chartType] ? ChartSubTypes[chartType] : [];
    return (
      subTypes?.map(subType => ({
        label: subType,
        value: subType
      })) ?? []
    );
  }, [chartType]);

  const renderContent = useCallback(() => {
    if (
      question.properties?.chartConfig.type === ChartType.LINE ||
      question.properties?.chartConfig.type === ChartType.BAR
    ) {
      return (
        <>
          <Select
            label="xAxis"
            name={questionAccessor("properties.chartConfig.xAxis")}
            value={question.properties?.chartConfig?.xAxis}
            disabled={disabled}
            options={[]}
            onChange={
              v => {}
              // updateField("restrictedFileTypes")(v as SelectValue[])
            }
            onlyTriggerChangeWhenBlur
          />
          <MultiSelect
            width="100"
            label="Series"
            name={questionAccessor("properties.chartConfig.series")}
            value={question.properties?.chartConfig?.series}
            disabled={disabled}
            options={[]}
            onChange={
              v => {}
              // updateField("restrictedFileTypes")(v as SelectValue[])
            }
            onlyTriggerChangeWhenBlur
          />
        </>
      );
    } else {
      return (
        <>
          <Select
            label="label"
            name={questionAccessor("properties.chartConfig.xAxis")}
            value={question.properties?.chartConfig?.label}
            disabled={disabled}
            options={[]}
            onChange={
              v => {}
              // updateField("restrictedFileTypes")(v as SelectValue[])
            }
            onlyTriggerChangeWhenBlur
          />
          <MultiSelect
            width="100"
            label="value"
            name={questionAccessor("properties.chartConfig.series")}
            value={question.properties?.chartConfig?.value}
            disabled={disabled}
            options={[]}
            onChange={
              v => {}
              // updateField("restrictedFileTypes")(v as SelectValue[])
            }
            onlyTriggerChangeWhenBlur
          />
        </>
      );
    }
  }, [question, path, d, isExpanded, onChangeExpanded, disabled]);

  return (
    <Stack gap="100" className="multi-level-properties">
      <Select
        label="subType"
        name={questionAccessor("properties.chartConfig.subType")}
        value={question.properties?.chartConfig?.subType}
        disabled={disabled}
        options={subTypeOptions}
        onChange={
          v => {}
          // updateField("restrictedFileTypes")(v as SelectValue[])
        }
        onlyTriggerChangeWhenBlur
      />
      {renderContent()}
    </Stack>
  );
};

ChartTypeProperties.displayName = "ChartTypeProperties";
